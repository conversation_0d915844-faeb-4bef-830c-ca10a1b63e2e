/* Global styles for Tabble */

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: #121212;
}
::-webkit-scrollbar-thumb {
  background: #FFA500;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #E69500;
}

/* Selection color */
::selection {
  background-color: rgba(255, 165, 0, 0.3);
  color: #FFFFFF;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Luxury arrow bullet points */
.luxury-list {
  list-style: none;
  padding-left: 1.5rem;
}

.luxury-list li {
  position: relative;
  margin-bottom: 0.75rem;
}

.luxury-list li::before {
  content: "→";
  position: absolute;
  left: -1.5rem;
  color: #FFA500;
  font-weight: bold;
}

/* Elegant transitions */
a, button, .MuiCard-root, .MuiPaper-root {
  transition: all 0.3s ease;
}

/* Luxury gradient text */
.gradient-text {
  background: linear-gradient(to right, #FFA500, #FFB733);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Elegant divider */
.elegant-divider {
  width: 60px;
  height: 3px;
  background-color: #FFA500;
  margin: 1.5rem auto;
}

/* Luxury card hover effect */
.luxury-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.luxury-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Subtle orange border */
.orange-border {
  border: 1px solid rgba(255, 165, 0, 0.2);
}

/* Luxury button hover effect */
.luxury-button {
  position: relative;
  overflow: hidden;
}

.luxury-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 165, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.luxury-button:hover::after {
  left: 100%;
}
