import sqlite3
import os
import shutil
from datetime import datetime

# Define database names
source_db = "tabble_new.db"
target_db = "tabble_test.db"

# Check if the target database already exists and remove it
if os.path.exists(target_db):
    os.remove(target_db)
    print(f"Removed existing {target_db}")

# Copy the source database to create a new one
try:
    # Option 1: Copy the entire database file
    shutil.copy2(source_db, target_db)
    print(f"Created {target_db} by copying {source_db}")
    
    # Connect to the new database and verify
    conn = sqlite3.connect(target_db)
    cursor = conn.cursor()
    
    # Get table names to verify
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print(f"Tables in {target_db}:")
    for table in tables:
        print(f"- {table[0]}")
    
    # Update the hotel name in settings to differentiate
    cursor.execute("UPDATE settings SET hotel_name = 'Test Hotel', updated_at = ? WHERE id = 1", 
                  (datetime.now().isoformat(),))
    conn.commit()
    
    # Verify the change
    cursor.execute("SELECT hotel_name FROM settings")
    hotel_name = cursor.fetchone()
    print(f"Updated hotel name in settings to: {hotel_name[0]}")
    
    conn.close()
    print(f"Successfully created and configured {target_db}")
    
except Exception as e:
    print(f"Error creating test database: {e}")

# Update hotels.csv to include the new database
try:
    # Check if hotels.csv exists
    if not os.path.exists("hotels.csv"):
        # Create the file with header and entries
        with open("hotels.csv", "w") as f:
            f.write("hotel_database,password\n")
            f.write(f"{source_db},myhotel\n")
            f.write(f"{target_db},testhotel\n")
        print("Created hotels.csv with both databases")
    else:
        # Check if the test database is already in the file
        with open("hotels.csv", "r") as f:
            lines = f.readlines()
        
        found = False
        for line in lines:
            if target_db in line:
                found = True
                break
        
        if not found:
            # Append the new database
            with open("hotels.csv", "a") as f:
                f.write(f"{target_db},testhotel\n")
            print(f"Added {target_db} to hotels.csv")
        else:
            print(f"{target_db} already exists in hotels.csv")
            
except Exception as e:
    print(f"Error updating hotels.csv: {e}")

print("Done! You can now test the database selection feature with both databases.")
