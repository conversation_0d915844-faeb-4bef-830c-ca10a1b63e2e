<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dish Analysis - Tabble</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">
    <style>
        body {
            background-color: #000000;
            color: #FFFFFF;
            font-family: 'Montserrat', sans-serif;
        }
        .card {
            background-color: #121212;
            border-top: 4px solid #FFA500;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-title {
            color: #FFA500;
        }
        .nav-tabs .nav-link {
            color: #FFFFFF;
        }
        .nav-tabs .nav-link.active {
            background-color: transparent;
            color: #FFA500;
            border-color: #FFA500 #FFA500 transparent;
        }
        .btn-primary {
            background-color: #FFA500;
            border-color: #FFA500;
        }
        .btn-outline-primary {
            color: #FFA500;
            border-color: #FFA500;
        }
        .btn-outline-primary:hover {
            background-color: #FFA500;
            color: #FFFFFF;
        }
        .table {
            color: #FFFFFF;
        }
        .table thead th {
            background-color: #000000;
            color: #FFA500;
            border-color: #333333;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(255, 165, 0, 0.05);
        }
        .category-badge {
            background-color: rgba(255, 165, 0, 0.1);
            color: #FFA500;
            border-radius: 4px;
            padding: 2px 8px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Dish Analysis</h1>

        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link" href="/analysis">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/analysis/customer">Customer Analysis</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/analysis/dish">Dish Analysis</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/analysis/chef">Chef Analysis</a>
            </li>
        </ul>

        <div class="alert alert-info">
            <h5>Dish Analysis Dashboard</h5>
            <p>This dashboard provides detailed insights into dish performance and menu optimization. For a better experience with interactive charts, please use the React frontend.</p>
            <a href="/" class="btn btn-primary">Go to Home Page</a>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">0</h1>
                        <h5 class="card-title">Top Selling Dishes</h5>
                        <p class="card-text">These dishes generate the most revenue for your hotel</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">0</h1>
                        <h5 class="card-title">Menu Categories</h5>
                        <p class="card-text">Number of different dish categories on your menu</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">$0</h1>
                        <h5 class="card-title">Total Revenue</h5>
                        <p class="card-text">Total revenue generated from all dishes</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">Top Selling Dishes</h5>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Dish</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Quantity Sold</th>
                            <th>Revenue</th>
                            <th>Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>No data available</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">Menu Insights & Recommendations</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="border-start border-warning ps-3 mb-3">
                            <h6>Top Performing Category</h6>
                            <p class="small">No category data available. Consider expanding this category with new dishes.</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-start border-warning ps-3 mb-3">
                            <h6>Menu Balance</h6>
                            <p class="small">Your menu categories are well-balanced. Consider refreshing or promoting underperforming categories.</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-start border-warning ps-3 mb-3">
                            <h6>Star Dishes</h6>
                            <p class="small">Your top 3 dishes account for 0% of total revenue. Feature these prominently and consider creating variations.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5 mb-4">
            <p>For a better experience with interactive charts and detailed analytics, please use the React frontend.</p>
            <div class="btn-group">
                <a href="/analysis" class="btn btn-outline-primary">Overview</a>
                <a href="/analysis/customer" class="btn btn-outline-primary">Customer Analysis</a>
                <a href="/analysis/chef" class="btn btn-outline-primary">Chef Analysis</a>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', path='/js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
