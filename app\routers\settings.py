from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import Optional, List
import os
import shutil
import csv
from datetime import datetime, timezone

from ..database import get_db, Settings, switch_database, get_current_database
from ..models.settings import Settings as SettingsModel, SettingsUpdate
from ..models.database_config import DatabaseEntry, DatabaseList, DatabaseSelectRequest, DatabaseSelectResponse

router = APIRouter(
    prefix="/settings",
    tags=["settings"],
    responses={404: {"description": "Not found"}},
)


# Get available databases from hotels.csv
@router.get("/databases", response_model=DatabaseList)
def get_databases():
    try:
        databases = []
        with open("hotels.csv", "r") as file:
            reader = csv.DictReader(file)
            for row in reader:
                databases.append(DatabaseEntry(
                    database_name=row["hotel_database"],
                    password=row["password"]
                ))

        # Return only database names, not passwords
        return {"databases": [{"database_name": db.database_name, "password": "********"} for db in databases]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading database configuration: {str(e)}")


# Get current database name
@router.get("/current-database")
def get_current_db():
    return {"database_name": get_current_database()}


# Switch database
@router.post("/switch-database", response_model=DatabaseSelectResponse)
def select_database(request: DatabaseSelectRequest):
    try:
        # Verify database exists and password is correct
        with open("hotels.csv", "r") as file:
            reader = csv.DictReader(file)
            for row in reader:
                if row["hotel_database"] == request.database_name:
                    if row["password"] == request.password:
                        # Switch database
                        switch_database(request.database_name)
                        return {
                            "success": True,
                            "message": f"Successfully switched to database: {request.database_name}"
                        }
                    else:
                        raise HTTPException(status_code=401, detail="Invalid password")

        raise HTTPException(status_code=404, detail=f"Database '{request.database_name}' not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error switching database: {str(e)}")


# Get hotel settings
@router.get("/", response_model=SettingsModel)
def get_settings(db: Session = Depends(get_db)):
    # Get the first settings record or create one if it doesn't exist
    settings = db.query(Settings).first()

    if not settings:
        # Create default settings
        settings = Settings(
            hotel_name="Tabble Hotel",
            address="123 Main Street, City",
            contact_number="******-456-7890",
            email="<EMAIL>",
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)

    return settings


# Update hotel settings
@router.put("/", response_model=SettingsModel)
async def update_settings(
    hotel_name: str = Form(...),
    address: Optional[str] = Form(None),
    contact_number: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    tax_id: Optional[str] = Form(None),
    logo: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db)
):
    # Get existing settings or create new
    settings = db.query(Settings).first()

    if not settings:
        settings = Settings(
            hotel_name=hotel_name,
            address=address,
            contact_number=contact_number,
            email=email,
            tax_id=tax_id,
        )
        db.add(settings)
    else:
        # Update fields
        settings.hotel_name = hotel_name
        settings.address = address
        settings.contact_number = contact_number
        settings.email = email
        settings.tax_id = tax_id

    # Handle logo upload if provided
    if logo:
        # Create directory if it doesn't exist
        os.makedirs("app/static/images/logo", exist_ok=True)

        # Save logo
        logo_path = f"app/static/images/logo/hotel_logo_{logo.filename}"
        with open(logo_path, "wb") as buffer:
            shutil.copyfileobj(logo.file, buffer)

        # Update settings with logo path
        settings.logo_path = f"/static/images/logo/hotel_logo_{logo.filename}"

    # Update timestamp
    settings.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(settings)

    return settings
