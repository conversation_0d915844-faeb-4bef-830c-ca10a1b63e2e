<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - Tabble</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">
    <style>
        body {
            background-color: #000000;
            color: #FFFFFF;
            font-family: 'Montserrat', sans-serif;
        }
        .card {
            background-color: #121212;
            border-top: 4px solid #FFA500;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-title {
            color: #FFA500;
        }
        .nav-tabs .nav-link {
            color: #FFFFFF;
        }
        .nav-tabs .nav-link.active {
            background-color: transparent;
            color: #FFA500;
            border-color: #FFA500 #FFA500 transparent;
        }
        .btn-primary {
            background-color: #FFA500;
            border-color: #FFA500;
        }
        .btn-outline-primary {
            color: #FFA500;
            border-color: #FFA500;
        }
        .btn-outline-primary:hover {
            background-color: #FFA500;
            color: #FFFFFF;
        }
        .table {
            color: #FFFFFF;
        }
        .table thead th {
            background-color: #000000;
            color: #FFA500;
            border-color: #333333;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(255, 165, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Analytics Dashboard</h1>

        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link active" href="/analysis">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/analysis/customer">Customer Analysis</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/analysis/dish">Dish Analysis</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/analysis/chef">Chef Analysis</a>
            </li>
        </ul>

        <div class="alert alert-info">
            <h5>Welcome to the Analytics Dashboard</h5>
            <p>This dashboard provides comprehensive analytics for your hotel's performance. For a better experience, please use the React frontend.</p>
            <a href="/" class="btn btn-primary">Go to Home Page</a>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">$0</h1>
                        <h5 class="card-title">Total Sales</h5>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">0</h1>
                        <h5 class="card-title">Total Customers</h5>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">0</h1>
                        <h5 class="card-title">Total Orders</h5>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 text-warning">$0</h1>
                        <h5 class="card-title">Avg. Order Value</h5>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Top Customers</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>No data available</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Top Selling Dishes</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Dish</th>
                                    <th>Quantity Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>No data available</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5 mb-4">
            <p>For a better experience with interactive charts and detailed analytics, please use the React frontend.</p>
            <div class="btn-group">
                <a href="/analysis/customer" class="btn btn-outline-primary">Customer Analysis</a>
                <a href="/analysis/dish" class="btn btn-outline-primary">Dish Analysis</a>
                <a href="/analysis/chef" class="btn btn-outline-primary">Chef Analysis</a>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', path='/js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
